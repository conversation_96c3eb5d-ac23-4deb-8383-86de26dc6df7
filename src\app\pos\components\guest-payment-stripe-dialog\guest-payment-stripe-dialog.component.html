<mat-dialog-content>
  <div class="guest-payment-dialog">
    <!-- Close Button -->
    <div class="close-button-container">
      <button mat-icon-button class="close-button" (click)="closeDialog()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Environment Indicator -->
    <!-- <div class="environment-indicator" [class.test-mode]="isTestEnvironment()" [class.production-mode]="!isTestEnvironment()">
      <mat-icon>{{ isTestEnvironment() ? 'science' : 'security' }}</mat-icon>
      <span>{{ isTestEnvironment() ? 'TEST MODE' : 'PRODUCTION MODE' }}</span>
    </div> -->

    <!-- Success State -->
    <div *ngIf="paymentSuccess()" class="success-state">
      <div class="success-icon">
        <mat-icon color="primary">check_circle</mat-icon>
      </div>
      <h2 class="success-title">Payment Successful!</h2>
      <p class="success-message">Your order has been placed successfully.</p>
      <div class="success-details">
        <p>Amount: <strong>{{ formatCurrency(totalAmount) }}</strong></p>
        <p>Thank you for your order!</p>
      </div>
    </div>

    <!-- Payment Form -->
    <div *ngIf="!paymentSuccess()" class="payment-form-container">
      <!-- Header -->
      <!-- <h2 class="dialog-title">Guest Payment</h2> -->
      <p class="dialog-subtitle">Complete your order with secure card payment</p>

      <!-- Order Summary -->
      <div class="order-summary">
        <h3>Order Summary</h3>
        <div class="order-items">
          <div *ngFor="let item of orderItems" class="order-item">
            <span class="item-name">{{ item.name || item.itemName }}</span>
            <span class="item-quantity">x{{ item.quantity }}</span>
            <span class="item-price">{{ formatCurrency(item.price * item.quantity) }}</span>
          </div>
        </div>
        <div class="order-total">
          <strong>Total: {{ formatCurrency(totalAmount) }}</strong>
        </div>
      </div>

      <!-- Payment Form -->
      <form [formGroup]="paymentForm" class="payment-form">
        
        <!-- TEST ENVIRONMENT: Legacy Card Form -->
        <div *ngIf="isTestEnvironment()" class="test-payment-form">
          <div class="test-mode-notice">
            <mat-icon>info</mat-icon>
            <span>Test mode: Use test card **************** for testing</span>
          </div>

          <!-- Card Number -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Card Number</mat-label>
            <input 
              matInput 
              formControlName="cardNumber"
              placeholder="4242 4242 4242 4242"
              maxlength="19"
              autocomplete="cc-number">
            <mat-icon matSuffix [class]="getCardTypeClass()">{{ getCardTypeIcon() }}</mat-icon>
            <mat-hint *ngIf="cardType()">{{ cardType() }}</mat-hint>
            <mat-error *ngIf="paymentForm.get('cardNumber')?.invalid && paymentForm.get('cardNumber')?.touched">
              <span *ngIf="paymentForm.get('cardNumber')?.errors?.['required']">Card number is required</span>
              <span *ngIf="!cardNumberValid() && paymentForm.get('cardNumber')?.value">Invalid card number</span>
            </mat-error>
          </mat-form-field>

          <!-- Expiry Date -->
          <div class="expiry-row">
            <mat-form-field appearance="outline" class="expiry-month">
              <mat-label>Month</mat-label>
              <mat-select formControlName="expiryMonth">
                <mat-option *ngFor="let month of expiryMonths" [value]="month.value">
                  {{ month.label }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="paymentForm.get('expiryMonth')?.invalid && paymentForm.get('expiryMonth')?.touched">
                Month is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="expiry-year">
              <mat-label>Year</mat-label>
              <mat-select formControlName="expiryYear">
                <mat-option *ngFor="let year of expiryYears" [value]="year.value">
                  {{ year.label }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="paymentForm.get('expiryYear')?.invalid && paymentForm.get('expiryYear')?.touched">
                Year is required
              </mat-error>
            </mat-form-field>
          </div>

          <div *ngIf="!expiryValid() && paymentForm.get('expiryMonth')?.value && paymentForm.get('expiryYear')?.value" 
               class="expiry-error">
            <mat-error>Card has expired</mat-error>
          </div>

          <!-- CVV -->
          <mat-form-field appearance="outline" class="cvv-field">
            <mat-label>CVV</mat-label>
            <input 
              matInput 
              formControlName="cvv"
              placeholder="123"
              maxlength="4"
              type="password"
              autocomplete="cc-csc">
            <mat-icon matSuffix matTooltip="3-4 digit security code on the back of your card">help_outline</mat-icon>
            <mat-error *ngIf="paymentForm.get('cvv')?.invalid && paymentForm.get('cvv')?.touched">
              <span *ngIf="paymentForm.get('cvv')?.errors?.['required']">CVV is required</span>
              <span *ngIf="!cvvValid() && paymentForm.get('cvv')?.value">Invalid CVV</span>
            </mat-error>
          </mat-form-field>
        </div>

        <!-- PRODUCTION ENVIRONMENT: Stripe Elements -->
        <div *ngIf="!isTestEnvironment()" class="production-payment-form">
          <div class="production-mode-notice">
            <mat-icon>security</mat-icon>
            <span>Secure payment processing with Stripe</span>
          </div>

          <!-- Stripe Card Element -->
          <div class="stripe-card-container">
            <label class="stripe-card-label">Card Details</label>
            <div class="stripe-card-element">
              <ngx-stripe-elements [stripe]="stripe" [elementsOptions]="elementsOptions">
                <ngx-stripe-card
                  [options]="cardOptions"
                  (change)="onStripeCardChange($event)">
                </ngx-stripe-card>
              </ngx-stripe-elements>
            </div>
          </div>
        </div>

        <!-- Cardholder Name (Both Environments) -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Cardholder Name</mat-label>
          <input 
            matInput 
            formControlName="cardholderName"
            placeholder="John Smith"
            autocomplete="cc-name">
          <mat-error *ngIf="paymentForm.get('cardholderName')?.invalid && paymentForm.get('cardholderName')?.touched">
            <span *ngIf="paymentForm.get('cardholderName')?.errors?.['required']">Cardholder name is required</span>
            <span *ngIf="paymentForm.get('cardholderName')?.value && paymentForm.get('cardholderName')?.value.length < 2">Name must be at least 2 characters</span>
          </mat-error>
        </mat-form-field>
      </form>

      <!-- Error Message -->
      <div *ngIf="errorMessage()" class="error-message">
        <mat-error>{{ errorMessage() }}</mat-error>
      </div>

      <!-- Security Notice -->
      <div class="security-notice">
        <mat-icon>security</mat-icon>
        <span>Your payment information is secure and encrypted</span>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <button 
          mat-stroked-button 
          class="cancel-btn" 
          (click)="closeDialog()"
          [disabled]="isProcessing()">
          Cancel
        </button>
        
        <button 
          mat-raised-button 
          color="primary"
          class="pay-btn"
          (click)="processPayment()"
          [disabled]="!isFormValid() || isProcessing()">
          <mat-spinner *ngIf="isProcessing()" diameter="20"></mat-spinner>
          <span *ngIf="!isProcessing()">Pay {{ formatCurrency(totalAmount) }}</span>
          <span *ngIf="isProcessing()">Processing...</span>
        </button>
      </div>
    </div>
  </div>
</mat-dialog-content>
