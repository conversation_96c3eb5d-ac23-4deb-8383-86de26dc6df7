import { Component, OnInit } from '@angular/core';
import { MultiScreenService } from '../../services/multi-screen.service';

@Component({
  selector: 'app-multi-screen-test',
  template: `
    <div class="multi-screen-test-container">
      <h2>Multi-Screen Test Component</h2>
      
      <div class="status-section">
        <h3>Screen Detection Status</h3>
        <p><strong>Status:</strong> {{ screenStatus }}</p>
        <p><strong>Screen Count:</strong> {{ screenCount }}</p>
        <p><strong>Has Multiple Screens:</strong> {{ hasMultipleScreens ? 'Yes' : 'No' }}</p>
        <p><strong>Screen Management API:</strong> {{ isScreenManagementSupported ? 'Supported' : 'Not Supported' }}</p>
      </div>

      <div class="screen-details" *ngIf="screenDetails">
        <h3>Screen Details</h3>
        <div *ngFor="let screen of screenDetails.screens; let i = index" class="screen-info">
          <h4>Screen {{ i + 1 }} {{ screen.isPrimary ? '(Primary)' : '(Secondary)' }}</h4>
          <ul>
            <li>Width: {{ screen.width }}px</li>
            <li>Height: {{ screen.height }}px</li>
            <li>Available Width: {{ screen.availWidth }}px</li>
            <li>Available Height: {{ screen.availHeight }}px</li>
            <li>Left Position: {{ screen.left }}px</li>
            <li>Top Position: {{ screen.top }}px</li>
          </ul>
        </div>
      </div>

      <div class="test-buttons">
        <h3>Test Actions</h3>
        <button (click)="testOpenOnSecondaryScreen()" [disabled]="!hasMultipleScreens">
          Open Test Window on Secondary Screen
        </button>
        <button (click)="testOpenRegularWindow()">
          Open Regular Test Window
        </button>
        <button (click)="refreshScreens()">
          Refresh Screen Detection
        </button>
        <button (click)="requestPermissions()">
          Request Window Management Permission
        </button>
      </div>

      <div class="console-output">
        <h3>Console Output</h3>
        <div class="console-log">
          <div *ngFor="let log of consoleLogs" [ngClass]="'log-' + log.type">
            {{ log.timestamp }} - {{ log.message }}
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .multi-screen-test-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    
    .status-section, .screen-details, .test-buttons, .console-output {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    
    .screen-info {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 3px;
    }
    
    .screen-info ul {
      margin: 5px 0;
      padding-left: 20px;
    }
    
    .test-buttons button {
      margin: 5px 10px 5px 0;
      padding: 10px 15px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }
    
    .test-buttons button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    
    .test-buttons button:hover:not(:disabled) {
      background-color: #0056b3;
    }
    
    .console-log {
      max-height: 200px;
      overflow-y: auto;
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 3px;
      font-family: monospace;
      font-size: 12px;
    }
    
    .log-info { color: #007bff; }
    .log-warn { color: #ffc107; }
    .log-error { color: #dc3545; }
  `]
})
export class MultiScreenTestComponent implements OnInit {
  screenStatus: string = '';
  screenCount: number = 0;
  hasMultipleScreens: boolean = false;
  isScreenManagementSupported: boolean = false;
  screenDetails: any = null;
  consoleLogs: Array<{timestamp: string, message: string, type: string}> = [];

  constructor(private multiScreenService: MultiScreenService) {}

  ngOnInit(): void {
    this.updateScreenInfo();
    this.log('Multi-screen test component initialized', 'info');
  }

  updateScreenInfo(): void {
    this.screenStatus = this.multiScreenService.getMultiScreenStatus();
    this.screenCount = this.multiScreenService.getScreens().length;
    this.hasMultipleScreens = this.multiScreenService.hasMultipleScreens();
    this.isScreenManagementSupported = this.multiScreenService.isScreenManagementAvailable();
    this.screenDetails = this.multiScreenService.getScreenDetails();
    
    this.log(`Screen info updated: ${this.screenCount} screens detected`, 'info');
  }

  testOpenOnSecondaryScreen(): void {
    this.log('Testing window opening on secondary screen...', 'info');
    const testUrl = 'data:text/html,<html><body><h1>Test Window on Secondary Screen</h1><p>This window should be on your extended screen!</p></body></html>';
    
    const testWindow = this.multiScreenService.openWindowOnSecondaryScreen(
      testUrl,
      'test-secondary',
      800,
      600
    );
    
    if (testWindow) {
      this.log('Test window opened successfully', 'info');
    } else {
      this.log('Failed to open test window', 'error');
    }
  }

  testOpenRegularWindow(): void {
    this.log('Testing regular window opening...', 'info');
    const testUrl = 'data:text/html,<html><body><h1>Regular Test Window</h1><p>This is a regular window for comparison.</p></body></html>';
    
    const testWindow = window.open(testUrl, 'test-regular', 'width=600,height=400');
    
    if (testWindow) {
      this.log('Regular test window opened successfully', 'info');
    } else {
      this.log('Failed to open regular test window', 'error');
    }
  }

  async refreshScreens(): Promise<void> {
    this.log('Refreshing screen detection...', 'info');
    try {
      await this.multiScreenService.refreshScreens();
      this.updateScreenInfo();
      this.log('Screen detection refreshed', 'info');
    } catch (error) {
      this.log(`Error refreshing screens: ${error}`, 'error');
    }
  }

  async requestPermissions(): Promise<void> {
    this.log('Requesting window management permissions...', 'info');
    try {
      const granted = await this.multiScreenService.requestWindowManagementPermission();
      if (granted) {
        this.log('Window management permission granted', 'info');
        await this.refreshScreens();
      } else {
        this.log('Window management permission denied or not available', 'warn');
      }
    } catch (error) {
      this.log(`Error requesting permissions: ${error}`, 'error');
    }
  }

  private log(message: string, type: 'info' | 'warn' | 'error' = 'info'): void {
    const timestamp = new Date().toLocaleTimeString();
    this.consoleLogs.unshift({ timestamp, message, type });
    
    // Keep only last 50 logs
    if (this.consoleLogs.length > 50) {
      this.consoleLogs = this.consoleLogs.slice(0, 50);
    }
    
    // Also log to browser console
    console.log(`[MultiScreenTest] ${message}`);
  }
}
