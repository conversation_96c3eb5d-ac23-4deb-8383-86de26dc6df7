<div class="container-fluid pos-container">
  <div class="row justify-content-center">
    <div class="col-12 col-md-12 col-lg-12">
      <!-- Merchant + School Selection Section -->
      <div class="row section mat-elevation-z2 mb-4">
        <div *ngIf="canteenListVisible" class="col-lg-6 col-md-6 col-sm-12">
          <div *ngIf="merchantFormGroup && listCanteens.length > 1" [formGroup]="merchantFormGroup">
            <mat-form-field appearance="outline">
              <mat-label>Merchant</mat-label>
              <mat-select formControlName="canteen" required>
                <mat-option *ngFor="let canteen of listCanteens" [value]="canteen.CanteenId">
                  {{ canteen.CanteenName }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-12">
          <div *ngIf="schoolFormGroup" [formGroup]="schoolFormGroup">
            <mat-form-field appearance="outline">
              <mat-label>School</mat-label>
              <mat-select formControlName="school">
                <mat-option *ngFor="let school of this.listSchools" [value]="school.SchoolId">
                  {{ school.Name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>

      <div class="pos-buttons">
        <div class="row">
          <div class="col-12 col-md-6 mb-3">
            <mat-card class="pos-card merchant-card" (click)="openMerchantView()">
              <mat-card-content class="pos-card-content">
                <mat-icon class="pos-icon">store</mat-icon>
                <h3 class="pos-card-title">Merchant View</h3>
                <p class="pos-card-description">
                  Access the merchant interface for order management and processing
                </p>
              </mat-card-content>
            </mat-card>
          </div>
          
          <div class="col-12 col-md-6 mb-3">
            <mat-card class="pos-card student-card" (click)="openStudentView()">
              <mat-card-content class="pos-card-content">
                <mat-icon class="pos-icon">school</mat-icon>
                <h3 class="pos-card-title">Student View</h3>
                <p class="pos-card-description">
                  Access the student interface for placing orders and viewing menus
                </p>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
