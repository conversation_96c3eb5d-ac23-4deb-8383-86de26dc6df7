import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { v4 as uuidv4 } from 'uuid';
import { ActivatedRoute } from '@angular/router';
import { FormGroup, FormControl } from '@angular/forms';

// ngrx
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import { CanteenState } from '../../../states';
import { canteenStateSelector } from '../../../states/canteen/canteen.selectors';
import * as canteenActions from '../../../states/canteen/canteen.actions';

// Models
import { UserCashless, Canteen, CanteenSchool } from '../../../sharedModels';

// Services
import { MultiScreenService } from '../../services/multi-screen.service';

@Component({
  selector: 'app-pos',
  templateUrl: './pos.component.html',
  styleUrls: ['./pos.component.scss'],
})
export class PosComponent implements OnInit, OnDestroy {
  SCHOOL_ID = 52243;
  showStudentDropdown = false;
  selectedStudent: UserCashless | null = null;

  // Merchant dropdown properties
  merchantFormGroup: FormGroup;
  listCanteens: Canteen[] = [];
  canteenListVisible: boolean = true;
  currentCanteenId: number;

  // School dropdown properties
  schoolFormGroup: FormGroup;
  listSchools: CanteenSchool[] = [];

  // Subscriptions
  private subscription: Subscription;
  private listLoaded: boolean = false;
  private selectedCanteen: number;

  constructor(
    private route: ActivatedRoute,
    private canteenStore: Store<{ canteen: CanteenState }>,
    private multiScreenService: MultiScreenService
  ) {}

  ngOnInit(): void {
    // Check if we're coming from the canteen/students route
    this.route.url.subscribe(() => {
      this.showStudentDropdown = window.location.pathname.includes('canteen/students');
    });

    // Initialize multi-screen detection
    this.initializeMultiScreenSupport();

    // Initialize merchant dropdown
    this.subscription = this.canteenStore
      .pipe(select(canteenStateSelector))
      .subscribe((state: CanteenState) => {
        this.currentCanteenId = state?.selected?.CanteenId;
        if (!this.listLoaded) {
          this.listCanteens = [...state.list];

          if (state.dataLoaded) {
            this.listLoaded = true;

            if (this.listCanteens && this.listCanteens.length > 0) {
              this.canteenListVisible = this.listCanteens != null && this.listCanteens.length > 1;
              this.createMerchantForm(state.selected);

              // Initialize school dropdown if merchant is selected
              if (state.selected) {
                this.initializeSchoolDropdown(state);
              }
              return;
            }
          }
        }

        // Update school dropdown when merchant changes
        if (state.selected && state.selected.CanteenId !== this.selectedCanteen) {
          this.selectedCanteen = state.selected.CanteenId;
          this.initializeSchoolDropdown(state);
        }
      });
  }

  private async initializeMultiScreenSupport(): Promise<void> {
    // Request permissions for modern browsers
    try {
      await this.multiScreenService.requestWindowManagementPermission();
    } catch (error) {
      console.log('[POS] Window management permission not available or denied:', error);
    }

    // Log multi-screen capabilities for debugging
    setTimeout(() => {
      console.log('[POS] Multi-screen status:', this.multiScreenService.getMultiScreenStatus());
      console.log('[POS] Screen details:', this.multiScreenService.getScreenDetails());

      // Force refresh screen detection
      this.multiScreenService.refreshScreens();
    }, 1000); // Delay to allow screen detection to complete
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  openMerchantView(): void {
    this.openPosTab('merchant');
  }

  openStudentView(): void {
    this.openPosTab('student');
  }

  private openPosTab(viewType: 'merchant' | 'student'): void {
    const guid = uuidv4();
    const url = `/canteen/pos/tab?schoolId=${this.SCHOOL_ID}&guid=${guid}&viewType=${viewType}`;

    if (viewType === 'student') {
      // Try to open student view on secondary screen
      this.openStudentViewOnSecondaryScreen(url);
    } else {
      // Open merchant view normally
      window.open(url, '_blank');
    }
  }

  private openStudentViewOnSecondaryScreen(url: string): void {
    console.log('[POS] Attempting to open student view on secondary screen');
    console.log('[POS] Multi-screen status:', this.multiScreenService.getMultiScreenStatus());

    try {
      // Check if multiple screens are available
      if (this.multiScreenService.hasMultipleScreens()) {
        console.log('[POS] Multiple screens detected, opening on secondary screen');

        // Open window on secondary screen with optimal size for POS interface
        const studentWindow = this.multiScreenService.openWindowOnSecondaryScreen(
          url,
          'student-view',
          1400, // width - larger for POS interface
          900   // height
        );

        if (studentWindow) {
          console.log('[POS] Student view successfully opened');

          // Optional: Focus the new window after a brief delay
          setTimeout(() => {
            try {
              if (studentWindow && !studentWindow.closed) {
                studentWindow.focus();
              }
            } catch (error) {
              console.warn('[POS] Could not focus student window:', error);
            }
          }, 500);

          // Optional: Add event listener for window close
          this.addWindowCloseListener(studentWindow);
        } else {
          console.warn('[POS] Failed to open student view, this might be due to popup blockers');
          this.handleWindowOpenFailure(url);
        }
      } else {
        console.log('[POS] No secondary screen available, opening in regular window');
        this.openRegularWindow(url);
      }
    } catch (error) {
      console.error('[POS] Error in multi-screen window opening:', error);
      this.handleWindowOpenFailure(url);
    }
  }

  private openRegularWindow(url: string): void {
    try {
      const regularWindow = window.open(url, '_blank');
      if (!regularWindow) {
        console.warn('[POS] Regular window opening failed, popup might be blocked');
        this.showPopupBlockedMessage();
      }
    } catch (error) {
      console.error('[POS] Failed to open regular window:', error);
      this.showPopupBlockedMessage();
    }
  }

  private handleWindowOpenFailure(url: string): void {
    console.warn('[POS] All window opening methods failed, trying basic fallback');
    try {
      const fallbackWindow = window.open(url);
      if (!fallbackWindow) {
        this.showPopupBlockedMessage();
      }
    } catch (error) {
      console.error('[POS] Complete window opening failure:', error);
      this.showPopupBlockedMessage();
    }
  }

  private addWindowCloseListener(windowRef: Window): void {
    try {
      // Check if window is closed periodically
      const checkClosed = setInterval(() => {
        if (windowRef.closed) {
          console.log('[POS] Student view window was closed');
          clearInterval(checkClosed);
        }
      }, 1000);

      // Clear interval after 30 seconds to prevent memory leaks
      setTimeout(() => {
        clearInterval(checkClosed);
      }, 30000);
    } catch (error) {
      console.warn('[POS] Could not add window close listener:', error);
    }
  }

  private showPopupBlockedMessage(): void {
    console.warn('[POS] Popup blocked - user should allow popups for this site');
    // You could integrate with a notification service here
    // For now, just log the message
    alert('Please allow popups for this site to open the student view in a new window.');
  }

  onStudentSelected(student: UserCashless): void {
    this.selectedStudent = student;
    if (student) {
      // Open student view with the selected student
      const guid = uuidv4();
      const url = `/canteen/pos/tab?schoolId=${this.SCHOOL_ID}&guid=${guid}&viewType=student&userId=${student.UserId}`;

      // Use the same multi-screen logic for student-specific views
      this.openStudentViewOnSecondaryScreen(url);
    }
  }

  private createMerchantForm(selectedCanteen: Canteen): void {
    let checkExist = -1;

    if (selectedCanteen) {
      checkExist = this.listCanteens.findIndex(x => x.CanteenId == selectedCanteen.CanteenId);
    }

    // check if selected exist in the list
    let selected = this.listCanteens[0].CanteenId;

    if (checkExist >= 0) {
      selected = selectedCanteen.CanteenId;
    }

    this.merchantFormGroup = new FormGroup({
      canteen: new FormControl(selected),
    });

    // set selected
    this.setSelectedCanteen(selected);

    this.canteen.valueChanges.subscribe(val => {
      this.setSelectedCanteen(val);
    });
  }

  private initializeSchoolDropdown(state: CanteenState): void {
    if (state.selected && state.selected.Schools && state.selected.Schools.length > 0) {
      this.listSchools = state.selected.Schools.slice().sort((a, b) => {
        if (a.Name < b.Name) return -1;
        if (a.Name > b.Name) return 1;
        else return 0;
      });
      this.createSchoolForm(state.selectedSchool);
    }
  }

  private createSchoolForm(selectedSchool: CanteenSchool): void {
    let checkExist = -1;

    if (selectedSchool) {
      checkExist = this.listSchools.findIndex(x => x.SchoolId == selectedSchool.SchoolId);
    }

    // check if selected exist in the list
    let selected = this.listSchools[0].SchoolId;

    if (checkExist >= 0) {
      selected = selectedSchool.SchoolId;
    }

    this.schoolFormGroup = new FormGroup({
      school: new FormControl(selected),
    });

    // set selected
    this.setSelectedSchool(selected);

    this.school.valueChanges.subscribe(val => {
      this.setSelectedSchool(val);
    });
  }

  get canteen() {
    return this.merchantFormGroup.get('canteen');
  }

  get school() {
    return this.schoolFormGroup.get('school');
  }

  private setSelectedCanteen(id: number): void {
    let canteen = this.listCanteens.find(x => x.CanteenId == id);
    // dispatch new canteen if new canteen does not match the canteen in state
    if (canteen && canteen.CanteenId != this.currentCanteenId) {
      this.canteenStore.dispatch(canteenActions.SetSelectedCanteen({ canteen: canteen }));
    }
  }

  private setSelectedSchool(id: number): void {
    let school = this.listSchools.find(x => x.SchoolId == id);
    if (school) {
      // Update the SCHOOL_ID when a school is selected
      this.SCHOOL_ID = school.SchoolId;
      this.canteenStore.dispatch(canteenActions.SetSelectedSchool({ school: school }));
    }
  }
}
