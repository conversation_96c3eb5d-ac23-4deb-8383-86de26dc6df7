import { TestBed } from '@angular/core/testing';
import { MultiScreenService } from './multi-screen.service';

describe('MultiScreenService', () => {
  let service: MultiScreenService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(MultiScreenService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize with at least one screen', () => {
    const screens = service.getScreens();
    expect(screens.length).toBeGreaterThanOrEqual(1);
  });

  it('should have a primary screen', () => {
    const primaryScreen = service.getPrimaryScreen();
    expect(primaryScreen).toBeTruthy();
    expect(primaryScreen?.isPrimary).toBe(true);
  });

  it('should provide screen status information', () => {
    const status = service.getMultiScreenStatus();
    expect(status).toBeTruthy();
    expect(typeof status).toBe('string');
  });

  it('should provide detailed screen information', () => {
    const details = service.getScreenDetails();
    expect(details).toBeTruthy();
    expect(details.screenCount).toBeGreaterThanOrEqual(1);
    expect(Array.isArray(details.screens)).toBe(true);
  });

  it('should handle window opening gracefully', () => {
    // Mock window.open to prevent actual window opening during tests
    const originalWindowOpen = window.open;
    const mockWindow = { focus: jasmine.createSpy('focus'), closed: false } as any;
    spyOn(window, 'open').and.returnValue(mockWindow);

    const result = service.openWindowOnSecondaryScreen('http://test.com', 'test-window');
    
    expect(window.open).toHaveBeenCalled();
    expect(result).toBeTruthy();

    // Restore original window.open
    window.open = originalWindowOpen;
  });

  it('should calculate secondary screen position when available', () => {
    // If multiple screens are detected, should return position
    if (service.hasMultipleScreens()) {
      const position = service.getSecondaryScreenPosition(1200, 800);
      expect(position).toBeTruthy();
      expect(position?.width).toBeGreaterThan(0);
      expect(position?.height).toBeGreaterThan(0);
    } else {
      const position = service.getSecondaryScreenPosition(1200, 800);
      expect(position).toBeNull();
    }
  });

  it('should handle permission requests gracefully', async () => {
    // This test will pass regardless of browser support
    const hasPermission = await service.requestWindowManagementPermission();
    expect(typeof hasPermission).toBe('boolean');
  });

  it('should refresh screens without errors', async () => {
    await expectAsync(service.refreshScreens()).toBeResolved();
  });
});
