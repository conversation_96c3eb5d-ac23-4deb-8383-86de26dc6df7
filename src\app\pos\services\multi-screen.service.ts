import { Injectable } from '@angular/core';

export interface ScreenInfo {
  width: number;
  height: number;
  availWidth: number;
  availHeight: number;
  left: number;
  top: number;
  isPrimary: boolean;
}

export interface WindowPosition {
  left: number;
  top: number;
  width: number;
  height: number;
  description?: string;
}

/**
 * Service for managing multi-screen functionality in POS system
 * Uses modern Screen Management API with fallback for older browsers
 */
@Injectable({
  providedIn: 'root'
})
export class MultiScreenService {
  private screens: ScreenInfo[] = [];
  private isScreenManagementSupported = false;

  constructor() {
    this.initializeScreenDetection();
  }

  /**
   * Initialize screen detection using available APIs
   */
  private async initializeScreenDetection(): Promise<void> {
    try {
      // Check for modern Screen Management API (Chrome 100+)
      if ('getScreenDetails' in window) {
        await this.initializeModernScreenAPI();
      } else {
        // Fallback to basic screen detection
        this.initializeBasicScreenDetection();
      }
    } catch (error) {
      console.warn('Screen detection failed, using fallback:', error);
      this.initializeBasicScreenDetection();
    }
  }

  /**
   * Initialize using modern Screen Management API
   */
  private async initializeModernScreenAPI(): Promise<void> {
    try {
      // Request permission for screen management
      const permission = await navigator.permissions.query({ name: 'window-management' as any });

      if (permission.state === 'granted') {
        // Get detailed screen information
        const screenDetails = await (window as any).getScreenDetails();

        this.screens = screenDetails.screens.map((screen: any, index: number) => ({
          width: screen.width,
          height: screen.height,
          availWidth: screen.availWidth,
          availHeight: screen.availHeight,
          left: screen.left,
          top: screen.top,
          isPrimary: screen.isPrimary || index === 0
        }));

        this.isScreenManagementSupported = true;
        console.log('[MultiScreen] Modern screen management initialized with', this.screens.length, 'screens:');
        this.screens.forEach((screen, index) => {
          console.log(`[MultiScreen] Screen ${index + 1} (${screen.isPrimary ? 'Primary' : 'Secondary'}):`,
            `${screen.width}x${screen.height} at ${screen.left},${screen.top}`);
        });
      } else {
        throw new Error('Window management permission not granted');
      }
    } catch (error) {
      console.warn('[MultiScreen] Modern screen API failed:', error);
      this.initializeBasicScreenDetection();
    }
  }

  /**
   * Fallback to basic screen detection
   */
  private initializeBasicScreenDetection(): void {
    // Use basic screen properties
    const primaryScreen: ScreenInfo = {
      width: screen.width,
      height: screen.height,
      availWidth: screen.availWidth,
      availHeight: screen.availHeight,
      left: 0,
      top: 0,
      isPrimary: true
    };

    this.screens = [primaryScreen];

    // Enhanced detection for extended screens
    console.log('[MultiScreen] Screen detection details:');
    console.log('[MultiScreen] - Screen dimensions:', screen.width, 'x', screen.height);
    console.log('[MultiScreen] - Available dimensions:', screen.availWidth, 'x', screen.availHeight);
    console.log('[MultiScreen] - Window position:', window.screenX, 'x', window.screenY);
    console.log('[MultiScreen] - Window outer dimensions:', window.outerWidth, 'x', window.outerHeight);

    // Try to detect extended screen setup
    // Method 1: Check if we can detect screen boundaries beyond primary screen
    let secondaryScreenLeft = screen.width;

    // Method 2: Try different common secondary screen positions
    // Most common setups: secondary screen to the right or left of primary
    const commonSecondaryPositions = [
      { left: screen.width, description: 'Right of primary' },
      { left: -screen.width, description: 'Left of primary' },
      { left: screen.width * 1.5, description: 'Far right (different resolution)' },
      { left: -screen.width * 1.5, description: 'Far left (different resolution)' }
    ];

    console.log('[MultiScreen] Available secondary screen positions:', commonSecondaryPositions);

    // For now, assume the most common setup: secondary screen to the right
    const secondaryScreen: ScreenInfo = {
      width: screen.width,
      height: screen.height,
      availWidth: screen.availWidth,
      availHeight: screen.availHeight,
      left: secondaryScreenLeft,
      top: 0,
      isPrimary: false
    };

    this.screens.push(secondaryScreen);

    console.log('[MultiScreen] Created secondary screen configuration:');
    console.log('[MultiScreen] - Primary screen: 0,0 -', screen.width + 'x' + screen.height);
    console.log('[MultiScreen] - Secondary screen:', secondaryScreenLeft + ',0 -', screen.width + 'x' + screen.height);
    console.log('[MultiScreen] Total screens configured:', this.screens.length);
  }

  /**
   * Get all available screens
   */
  getScreens(): ScreenInfo[] {
    return [...this.screens];
  }

  /**
   * Get the primary screen
   */
  getPrimaryScreen(): ScreenInfo | null {
    return this.screens.find(screen => screen.isPrimary) || this.screens[0] || null;
  }

  /**
   * Get the secondary/extended screen
   */
  getSecondaryScreen(): ScreenInfo | null {
    return this.screens.find(screen => !screen.isPrimary) || null;
  }

  /**
   * Check if multiple screens are available
   */
  hasMultipleScreens(): boolean {
    return this.screens.length > 1;
  }

  /**
   * Calculate optimal window position for secondary screen
   */
  getSecondaryScreenPosition(windowWidth: number = 1200, windowHeight: number = 800): WindowPosition | null {
    const secondaryScreen = this.getSecondaryScreen();

    if (!secondaryScreen) {
      return null;
    }

    // Center the window on the secondary screen
    const left = secondaryScreen.left + Math.max(0, (secondaryScreen.availWidth - windowWidth) / 2);
    const top = secondaryScreen.top + Math.max(0, (secondaryScreen.availHeight - windowHeight) / 2);

    return {
      left: Math.round(left),
      top: Math.round(top),
      width: Math.min(windowWidth, secondaryScreen.availWidth),
      height: Math.min(windowHeight, secondaryScreen.availHeight)
    };
  }

  /**
   * Create alternative position for secondary screen testing
   */
  private createAlternativePosition(
    windowWidth: number,
    windowHeight: number,
    leftOffset: number,
    description: string
  ): WindowPosition | null {
    // Calculate position based on offset from primary screen
    const left = leftOffset;
    const top = 50; // Small top offset

    return {
      left: Math.round(left),
      top: Math.round(top),
      width: Math.min(windowWidth, screen.availWidth),
      height: Math.min(windowHeight, screen.availHeight),
      description: description
    };
  }

  /**
   * Open a window on the secondary screen if available
   */
  openWindowOnSecondaryScreen(
    url: string,
    windowName: string = '_blank',
    windowWidth: number = 1200,
    windowHeight: number = 800
  ): Window | null {
    try {
      // Try multiple positioning strategies for better secondary screen detection
      const positions = [
        this.getSecondaryScreenPosition(windowWidth, windowHeight),
        this.createAlternativePosition(windowWidth, windowHeight, screen.width, 'Right of primary'),
        this.createAlternativePosition(windowWidth, windowHeight, -screen.width, 'Left of primary'),
        this.createAlternativePosition(windowWidth, windowHeight, screen.width * 1.5, 'Far right')
      ];

      for (let i = 0; i < positions.length; i++) {
        const position = positions[i];

        if (position) {
          console.log(`[MultiScreen] Trying position strategy ${i + 1} (${position.description || 'Standard'}):`, position);

          const features = [
            `width=${position.width}`,
            `height=${position.height}`,
            `left=${position.left}`,
            `top=${position.top}`,
            'resizable=yes',
            'scrollbars=yes',
            'status=no',
            'toolbar=no',
            'menubar=no',
            'location=no',
            'directories=no',
            'copyhistory=no'
          ].join(',');

          console.log('[MultiScreen] Opening window with features:', features);

          // Use a unique window name to ensure it opens as a new window, not a tab
          const uniqueWindowName = windowName + '_' + Date.now() + '_' + i;
          const newWindow = window.open(url, uniqueWindowName, features);

          if (newWindow) {
            // Verify the window opened with correct position
            setTimeout(() => {
              try {
                if (newWindow && !newWindow.closed) {
                  console.log('[MultiScreen] Window successfully opened. Position check:');
                  console.log('[MultiScreen] - Expected left:', position.left, 'Actual left:', newWindow.screenX);
                  console.log('[MultiScreen] - Expected top:', position.top, 'Actual top:', newWindow.screenY);

                  if (Math.abs(newWindow.screenX - position.left) > 100) {
                    console.warn('[MultiScreen] Window position differs significantly from expected');
                  } else {
                    console.log('[MultiScreen] Window positioned correctly on secondary screen');
                  }
                }
              } catch (error) {
                // Cross-origin restrictions may prevent access to window properties
                console.log('[MultiScreen] Cannot verify window position due to security restrictions');
              }
            }, 500);

            return newWindow;
          } else {
            console.warn(`[MultiScreen] Strategy ${i + 1} failed - popup might be blocked`);
          }
        }
      }

      // All positioning strategies failed
      console.log('[MultiScreen] All positioning strategies failed, using fallback');
      return this.fallbackWindowOpen(url, windowName);

    } catch (error) {
      console.error('[MultiScreen] Error opening window on secondary screen:', error);
      return this.fallbackWindowOpen(url, windowName);
    }
  }

  /**
   * Fallback method for opening windows when multi-screen fails
   */
  private fallbackWindowOpen(url: string, windowName: string = '_blank'): Window | null {
    try {
      console.log('[MultiScreen] Using fallback window opening method');

      // Try to open with some basic positioning to move it away from the main window
      // Position it to the right side to simulate extended screen placement
      const features = [
        'width=1400',
        'height=900',
        'left=800', // Move it to the right
        'top=50',
        'resizable=yes',
        'scrollbars=yes',
        'status=no',
        'toolbar=no',
        'menubar=no',
        'location=no',
        'directories=no',
        'copyhistory=no'
      ].join(',');

      // Use unique window name to ensure new window
      const uniqueWindowName = windowName + '_fallback_' + Date.now();
      const newWindow = window.open(url, uniqueWindowName, features);

      if (!newWindow) {
        console.warn('[MultiScreen] Fallback window opening failed, trying basic window.open');
        // Try with minimal features
        return window.open(url, '_blank', 'width=1400,height=900,resizable=yes');
      }

      return newWindow;
    } catch (error) {
      console.error('[MultiScreen] Fallback window opening failed:', error);
      // Last resort - basic window.open without features
      try {
        return window.open(url, '_blank');
      } catch (finalError) {
        console.error('[MultiScreen] All window opening methods failed:', finalError);
        return null;
      }
    }
  }

  /**
   * Check if screen management API is supported and available
   */
  isScreenManagementAvailable(): boolean {
    return this.isScreenManagementSupported;
  }

  /**
   * Request permission for window management (for modern browsers)
   */
  async requestWindowManagementPermission(): Promise<boolean> {
    try {
      if ('permissions' in navigator && 'query' in navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'window-management' as any });
        return permission.state === 'granted';
      }
    } catch (error) {
      console.warn('Permission check failed:', error);
    }
    return false;
  }

  /**
   * Refresh screen information (useful when screens are added/removed)
   */
  async refreshScreens(): Promise<void> {
    await this.initializeScreenDetection();
  }

  /**
   * Get user-friendly status message about multi-screen availability
   */
  getMultiScreenStatus(): string {
    if (this.isScreenManagementSupported) {
      return this.hasMultipleScreens()
        ? `Multi-screen support active. ${this.screens.length} screens detected.`
        : 'Multi-screen support active. Only one screen detected.';
    } else {
      return this.hasMultipleScreens()
        ? `Basic multi-screen support. ${this.screens.length} screens detected (estimated).`
        : 'Single screen detected. Student view will open in a new window.';
    }
  }

  /**
   * Get detailed screen information for debugging
   */
  getScreenDetails(): any {
    return {
      isScreenManagementSupported: this.isScreenManagementSupported,
      screenCount: this.screens.length,
      screens: this.screens,
      hasMultipleScreens: this.hasMultipleScreens(),
      primaryScreen: this.getPrimaryScreen(),
      secondaryScreen: this.getSecondaryScreen()
    };
  }
}
