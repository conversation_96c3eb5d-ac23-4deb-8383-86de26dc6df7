import { Injectable } from '@angular/core';

export interface ScreenInfo {
  width: number;
  height: number;
  availWidth: number;
  availHeight: number;
  left: number;
  top: number;
  isPrimary: boolean;
}

export interface WindowPosition {
  left: number;
  top: number;
  width: number;
  height: number;
}

/**
 * Service for managing multi-screen functionality in POS system
 * Uses modern Screen Management API with fallback for older browsers
 */
@Injectable({
  providedIn: 'root'
})
export class MultiScreenService {
  private screens: ScreenInfo[] = [];
  private isScreenManagementSupported = false;

  constructor() {
    this.initializeScreenDetection();
  }

  /**
   * Initialize screen detection using available APIs
   */
  private async initializeScreenDetection(): Promise<void> {
    try {
      // Check for modern Screen Management API (Chrome 100+)
      if ('getScreenDetails' in window) {
        await this.initializeModernScreenAPI();
      } else {
        // Fallback to basic screen detection
        this.initializeBasicScreenDetection();
      }
    } catch (error) {
      console.warn('Screen detection failed, using fallback:', error);
      this.initializeBasicScreenDetection();
    }
  }

  /**
   * Initialize using modern Screen Management API
   */
  private async initializeModernScreenAPI(): Promise<void> {
    try {
      // Request permission for screen management
      const permission = await navigator.permissions.query({ name: 'window-management' as any });
      
      if (permission.state === 'granted') {
        // Get detailed screen information
        const screenDetails = await (window as any).getScreenDetails();
        
        this.screens = screenDetails.screens.map((screen: any, index: number) => ({
          width: screen.width,
          height: screen.height,
          availWidth: screen.availWidth,
          availHeight: screen.availHeight,
          left: screen.left,
          top: screen.top,
          isPrimary: screen.isPrimary || index === 0
        }));
        
        this.isScreenManagementSupported = true;
        console.log('Modern screen management initialized:', this.screens);
      } else {
        throw new Error('Window management permission not granted');
      }
    } catch (error) {
      console.warn('Modern screen API failed:', error);
      this.initializeBasicScreenDetection();
    }
  }

  /**
   * Fallback to basic screen detection
   */
  private initializeBasicScreenDetection(): void {
    // Use basic screen properties
    const primaryScreen: ScreenInfo = {
      width: screen.width,
      height: screen.height,
      availWidth: screen.availWidth,
      availHeight: screen.availHeight,
      left: 0,
      top: 0,
      isPrimary: true
    };

    this.screens = [primaryScreen];
    
    // Try to detect if there might be multiple screens
    // This is a heuristic approach for older browsers
    if (screen.availWidth < screen.width || window.outerWidth !== screen.availWidth) {
      // Might indicate multiple screens, add a hypothetical second screen
      const secondaryScreen: ScreenInfo = {
        width: screen.width,
        height: screen.height,
        availWidth: screen.availWidth,
        availHeight: screen.availHeight,
        left: screen.width,
        top: 0,
        isPrimary: false
      };
      this.screens.push(secondaryScreen);
    }

    console.log('Basic screen detection initialized:', this.screens);
  }

  /**
   * Get all available screens
   */
  getScreens(): ScreenInfo[] {
    return [...this.screens];
  }

  /**
   * Get the primary screen
   */
  getPrimaryScreen(): ScreenInfo | null {
    return this.screens.find(screen => screen.isPrimary) || this.screens[0] || null;
  }

  /**
   * Get the secondary/extended screen
   */
  getSecondaryScreen(): ScreenInfo | null {
    return this.screens.find(screen => !screen.isPrimary) || null;
  }

  /**
   * Check if multiple screens are available
   */
  hasMultipleScreens(): boolean {
    return this.screens.length > 1;
  }

  /**
   * Calculate optimal window position for secondary screen
   */
  getSecondaryScreenPosition(windowWidth: number = 1200, windowHeight: number = 800): WindowPosition | null {
    const secondaryScreen = this.getSecondaryScreen();
    
    if (!secondaryScreen) {
      return null;
    }

    // Center the window on the secondary screen
    const left = secondaryScreen.left + Math.max(0, (secondaryScreen.availWidth - windowWidth) / 2);
    const top = secondaryScreen.top + Math.max(0, (secondaryScreen.availHeight - windowHeight) / 2);

    return {
      left: Math.round(left),
      top: Math.round(top),
      width: Math.min(windowWidth, secondaryScreen.availWidth),
      height: Math.min(windowHeight, secondaryScreen.availHeight)
    };
  }

  /**
   * Open a window on the secondary screen if available
   */
  openWindowOnSecondaryScreen(
    url: string,
    windowName: string = '_blank',
    windowWidth: number = 1200,
    windowHeight: number = 800
  ): Window | null {
    try {
      const position = this.getSecondaryScreenPosition(windowWidth, windowHeight);

      if (position) {
        const features = [
          `width=${position.width}`,
          `height=${position.height}`,
          `left=${position.left}`,
          `top=${position.top}`,
          'resizable=yes',
          'scrollbars=yes',
          'status=yes',
          'toolbar=no',
          'menubar=no',
          'location=no'
        ].join(',');

        console.log('[MultiScreen] Opening window on secondary screen with features:', features);

        const newWindow = window.open(url, windowName, features);

        if (!newWindow) {
          console.warn('[MultiScreen] Failed to open window on secondary screen, popup might be blocked');
          return this.fallbackWindowOpen(url, windowName);
        }

        // Verify the window opened with correct position
        setTimeout(() => {
          try {
            if (newWindow && !newWindow.closed) {
              console.log('[MultiScreen] Window successfully opened on secondary screen');
              // Optional: Verify position
              if (Math.abs(newWindow.screenX - position.left) > 50) {
                console.warn('[MultiScreen] Window may not have opened on intended screen');
              }
            }
          } catch (error) {
            // Cross-origin restrictions may prevent access to window properties
            console.log('[MultiScreen] Cannot verify window position due to security restrictions');
          }
        }, 100);

        return newWindow;
      }

      // No secondary screen available
      console.log('[MultiScreen] No secondary screen available, using fallback');
      return this.fallbackWindowOpen(url, windowName);

    } catch (error) {
      console.error('[MultiScreen] Error opening window on secondary screen:', error);
      return this.fallbackWindowOpen(url, windowName);
    }
  }

  /**
   * Fallback method for opening windows when multi-screen fails
   */
  private fallbackWindowOpen(url: string, windowName: string = '_blank'): Window | null {
    try {
      console.log('[MultiScreen] Using fallback window opening method');

      // Try to open with some basic positioning to move it away from the main window
      const features = [
        'width=1200',
        'height=800',
        'left=100',
        'top=100',
        'resizable=yes',
        'scrollbars=yes',
        'status=yes',
        'toolbar=no',
        'menubar=no',
        'location=no'
      ].join(',');

      const newWindow = window.open(url, windowName, features);

      if (!newWindow) {
        console.warn('[MultiScreen] Fallback window opening failed, trying basic window.open');
        return window.open(url, windowName);
      }

      return newWindow;
    } catch (error) {
      console.error('[MultiScreen] Fallback window opening failed:', error);
      // Last resort - basic window.open without features
      try {
        return window.open(url, windowName);
      } catch (finalError) {
        console.error('[MultiScreen] All window opening methods failed:', finalError);
        return null;
      }
    }
  }

  /**
   * Check if screen management API is supported and available
   */
  isScreenManagementAvailable(): boolean {
    return this.isScreenManagementSupported;
  }

  /**
   * Request permission for window management (for modern browsers)
   */
  async requestWindowManagementPermission(): Promise<boolean> {
    try {
      if ('permissions' in navigator && 'query' in navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'window-management' as any });
        return permission.state === 'granted';
      }
    } catch (error) {
      console.warn('Permission check failed:', error);
    }
    return false;
  }

  /**
   * Refresh screen information (useful when screens are added/removed)
   */
  async refreshScreens(): Promise<void> {
    await this.initializeScreenDetection();
  }

  /**
   * Get user-friendly status message about multi-screen availability
   */
  getMultiScreenStatus(): string {
    if (this.isScreenManagementSupported) {
      return this.hasMultipleScreens()
        ? `Multi-screen support active. ${this.screens.length} screens detected.`
        : 'Multi-screen support active. Only one screen detected.';
    } else {
      return this.hasMultipleScreens()
        ? `Basic multi-screen support. ${this.screens.length} screens detected (estimated).`
        : 'Single screen detected. Student view will open in a new window.';
    }
  }

  /**
   * Get detailed screen information for debugging
   */
  getScreenDetails(): any {
    return {
      isScreenManagementSupported: this.isScreenManagementSupported,
      screenCount: this.screens.length,
      screens: this.screens,
      hasMultipleScreens: this.hasMultipleScreens(),
      primaryScreen: this.getPrimaryScreen(),
      secondaryScreen: this.getSecondaryScreen()
    };
  }
}
